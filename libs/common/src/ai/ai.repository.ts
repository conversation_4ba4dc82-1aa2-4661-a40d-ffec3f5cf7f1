import { db } from '@compass/database';

import type { CompanyData } from './ai.types';

async function getCompany(companyId: number): Promise<CompanyData | null> {
  const company = await db.company.findUnique({
    select: {
      id: true,
      name: true,
      currency: true,
      isFirstCategorizationDone: true,
      appFeaturesInProcessing: true,
      industry: { select: { name: true } },
      country: { select: { name: true, code: true } },
    },
    where: { id: companyId },
  });
  if (!company) return null;

  return {
    id: company.id,
    companyName: company.name,
    currency: company.currency,
    isFirstCategorizationDone: company.isFirstCategorizationDone,
    appFeaturesInProcessing: company.appFeaturesInProcessing,
    country: company?.country?.name ?? null,
    countryCode: company?.country?.code ?? null,
    industry: company?.industry?.name ?? null,
  };
}

export default { getCompany };

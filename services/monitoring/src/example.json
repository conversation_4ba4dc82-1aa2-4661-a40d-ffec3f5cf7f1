{"sqlQuery": "SELECT \"t\".* FROM \"TransactionForAiQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL", "reasoning": "The user wants to be notified when a new transaction is uncategorized. This requires monitoring all transactions for missing category assignments.", "instructions": ["This alert runs continuously, checking each new transaction as it's created or modified.", "It triggers automatically when any transaction is saved without a category.", "The alert monitors all transactions across all accounts for missing categorization.", "When viewing the alert, you'll see a list of all uncategorized transactions.", "Use this alert to ensure all transactions are properly categorized for accurate financial reporting and analysis.", "When notified, review the uncategorized transaction(s) and assign appropriate categories promptly."], "notification": {"notificationQuery": "SELECT SUM(ABS(CAST(\"t\".\"convertedAmount\" ->> 'EUR' AS DECIMAL))) AS \"total_amount\", 'EUR' AS \"currency\" FROM \"TransactionForAiQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL", "notificationMessage": "New uncategorized transaction(s) detected. Total uncategorized amount: **{total_amount} {currency}**."}, "evaluationDay": [], "displayQueries": [{"displayQuerySql": "SELECT \"t\".* FROM \"TransactionForAiDisplayQuery\" AS \"t\" LEFT JOIN \"LeafCategoryForAiQuery\" AS \"lc\" ON \"t\".\"leafCategoryId\" = \"lc\".\"id\" WHERE \"lc\".\"id\" IS NULL", "displayQueryType": "transaction", "displayQueryTitle": "Uncategorized Transactions"}], "evaluationFrequency": null}